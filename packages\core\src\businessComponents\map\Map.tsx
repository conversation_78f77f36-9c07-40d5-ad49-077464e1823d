'use client'

import { useCallback, useEffect, useRef, useState } from 'react'

import { useDebounceFn, useLocation } from '../../businessHooks'
import type {
  AMapMap,
  AMapMapOptions,
  MassMarkerClickEvent,
  MassMarkerInstance,
  MassMarkerPoint,
} from '../../types/amap'
import { generateOSSUrl } from '../../utils'

// 扩展的高德地图实例接口，添加原生方法
interface NativeAMapInstance extends AMapMap {
  setZoom: (zoom: number) => void
  getCenter: () => { lng: number; lat: number }
  setBounds: (bounds: unknown, immediately?: boolean, avoid?: number[]) => void
  setFitView: (overlays?: unknown, immediately?: boolean, avoid?: number[]) => void
}

interface MapProps {
  mapCenter?: {
    latitude: number
    longitude: number
  }
  points?: MassMarkerPoint[]
  onMarkerClick?: (point: MassMarkerPoint) => void
  zoom?: number
}

// 定义海量点样式 - 使用多种样式
const MASS_MARKER_STYLES = [
  {
    // 普通门店样式
    url: generateOSSUrl('/images/marker1.png'),
    anchor: { x: 6, y: 6 },
    size: { width: 24, height: 24 },
    zIndex: 10,
  },
  {
    // 选中门店样式
    url: generateOSSUrl('/images/marker2.png'),
    anchor: { x: 6, y: 6 },
    size: { width: 24, height: 24 },
    zIndex: 100,
  },
]

const Map = ({ mapCenter, points = [], onMarkerClick, zoom }: MapProps) => {
  const { longitude, latitude } = mapCenter || {}
  const { getMapInstance, resetAMapInstance, createMassMarkers } = useLocation()
  const mapInstanceRef = useRef<AMapMap | null>(null)
  const massMarksRef = useRef<MassMarkerInstance | null>(null)
  const mapContainerRef = useRef<HTMLDivElement>(null)
  const isInitializedRef = useRef(false)
  const [lastCenter, setLastCenter] = useState<string>('')

  const cleanupMap = useCallback(() => {
    if (massMarksRef.current) {
      massMarksRef.current.setMap(null)
      massMarksRef.current = null
    }
    if (mapInstanceRef.current) {
      console.log('清理地图实例')
      mapInstanceRef.current.destroy()
      mapInstanceRef.current = null
      isInitializedRef.current = false
      // 重置全局地图实例
      resetAMapInstance()
    }
  }, [resetAMapInstance])

  // 自动适配显示所有海量点标记
  const fitMapToMarkers = useCallback(() => {
    if (!mapInstanceRef.current || !points.length || !window.AMap) return

    console.log('自动适配显示所有海量点')

    try {
      if (points.length === 1) {
        // 只有一个点时，直接设置中心点和缩放级别
        const point = points[0]
        mapInstanceRef.current.setCenter([point.lnglat[0], point.lnglat[1]])
        // 使用原生高德地图实例方法设置缩放
        const nativeMap = mapInstanceRef.current as NativeAMapInstance
        nativeMap.setZoom(16)
        return
      }

      // 直接使用原生高德地图API创建边界
      const nativeMap = mapInstanceRef.current as NativeAMapInstance

      // 尝试使用更简单的方式：直接使用高德地图的setFitView方法
      if (nativeMap.setFitView) {
        // 使用内置方法自动调整地图视野
        nativeMap.setFitView(null, false, [50, 50, 50, 50])
        console.log('使用setFitView自动适配地图视野')
      } else {
        // 手动计算边界的备选方案
        console.log('手动计算并设置地图边界')

        // 计算所有点的最小和最大经纬度
        let minLng = Infinity
        let maxLng = -Infinity
        let minLat = Infinity
        let maxLat = -Infinity

        points.forEach((point) => {
          const lng = point.lnglat[0]
          const lat = point.lnglat[1]

          minLng = Math.min(minLng, lng)
          maxLng = Math.max(maxLng, lng)
          minLat = Math.min(minLat, lat)
          maxLat = Math.max(maxLat, lat)
        })

        // 创建边界点
        const southWest = new window.AMap.LngLat(minLng, minLat)
        const northEast = new window.AMap.LngLat(maxLng, maxLat)

        // 创建边界对象 - 由于类型定义中没有Bounds，这里使用类型断言
        const AMapWithBounds = window.AMap as typeof window.AMap & {
          Bounds: new (sw: unknown, ne: unknown) => unknown
        }
        const bounds = new AMapWithBounds.Bounds(southWest, northEast)

        // 设置地图视野
        nativeMap.setBounds(bounds, false, [50, 50, 50, 50])
      }

      // 获取当前地图中心
      const center = nativeMap.getCenter()
      console.log('自动适配后的地图中心:', center.lng, center.lat)
    } catch (error) {
      console.error('自动适配地图视野失败:', error)
    }
  }, [points])

  const initMassMarks = useCallback(() => {
    if (!mapInstanceRef.current || !points.length) return

    console.log('初始化海量点:', points.length, '个点')

    // 如果已存在海量点实例，先清除
    if (massMarksRef.current) {
      massMarksRef.current.setMap(null)
      massMarksRef.current = null
    }

    // 创建海量点实例
    const massMarks = createMassMarkers(points, {
      zIndex: 999, // 设置足够高的层级，确保显示在地图文字之上
      zooms: [3, 19],
      style: MASS_MARKER_STYLES, // 传入样式数组
    })

    if (massMarks) {
      // 将海量点添加到地图
      massMarks.setMap(mapInstanceRef.current)
      massMarksRef.current = massMarks
      console.log('海量点创建成功')

      // 添加点击事件监听
      if (onMarkerClick && massMarks.on) {
        massMarks.on('click', (event: MassMarkerClickEvent) => {
          console.log('海量点被点击:', event)
          // 从事件中获取点击的点数据
          if (event.data) {
            onMarkerClick(event.data)
          }
        })
        console.log('海量点点击事件监听已添加')
      }

      // 在创建海量点后，自动适配地图视野
      fitMapToMarkers()
    }
  }, [points, createMassMarkers, fitMapToMarkers, onMarkerClick])

  // 更新地图中心点
  const updateMapCenter = useCallback(() => {
    if (!mapInstanceRef.current || !longitude || !latitude) return

    const centerKey = `${longitude},${latitude}`
    if (lastCenter !== centerKey) {
      console.log('更新地图中心点:', { longitude, latitude, zoom })
      // 使用数组方式设置中心点
      mapInstanceRef.current.setCenter([longitude, latitude])

      // 如果传入了缩放级别，则设置缩放
      if (zoom !== undefined) {
        const nativeMap = mapInstanceRef.current as NativeAMapInstance
        nativeMap.setZoom(zoom)
        console.log('设置地图缩放级别:', zoom)
      }

      setLastCenter(centerKey)
    }
  }, [longitude, latitude, lastCenter, zoom])

  const initMap = useCallback(async () => {
    if (!mapContainerRef.current) {
      console.error('地图容器未找到')
      return
    }

    if (mapInstanceRef.current) {
      console.log('地图已初始化，更新中心点和标记')
      updateMapCenter()
      initMassMarks()
      return
    }

    try {
      const options: AMapMapOptions = {
        viewMode: '2D',
        zoom: 14,
      }

      console.log('开始初始化地图', { longitude, latitude })

      if (!window.AMap) {
        throw new Error('高德地图 API 未加载')
      }

      if (longitude && latitude) {
        options.center = [longitude, latitude]
        setLastCenter(`${longitude},${latitude}`)
      }

      const mapInstance = await getMapInstance('mapContainer', options)

      if (!mapInstance) {
        throw new Error('获取地图实例失败')
      }

      mapInstanceRef.current = mapInstance
      isInitializedRef.current = true
      console.log('地图初始化成功')

      // 初始化海量点
      initMassMarks()
    } catch (error) {
      console.error('地图初始化失败:', error)
      cleanupMap()
    }
  }, [longitude, latitude, getMapInstance, cleanupMap, initMassMarks, updateMapCenter])

  const { run: debouncedInitMap } = useDebounceFn(initMap, { wait: 300 })

  // 初始化地图
  useEffect(() => {
    debouncedInitMap()
    return cleanupMap
  }, [debouncedInitMap, cleanupMap])

  // 监听中心点变化，更新地图中心
  useEffect(() => {
    if (isInitializedRef.current) {
      updateMapCenter()
    }
  }, [longitude, latitude, updateMapCenter])

  // 监听 points 变化，更新海量点
  useEffect(() => {
    if (isInitializedRef.current) {
      initMassMarks()
    }
  }, [points, initMassMarks])

  return (
    <div
      ref={mapContainerRef}
      id="mapContainer"
      className={`h-full w-full rounded-[4px]`}
      style={{
        height: '100%',
        width: '100%',
        minHeight: '336px',
      }}
    />
  )
}

export default Map
