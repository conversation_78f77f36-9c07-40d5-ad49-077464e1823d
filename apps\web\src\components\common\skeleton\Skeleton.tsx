'use client'
import React, { useMemo } from 'react'
import { Skeleton } from 'antd'

/**
 * Skeleton 组件
 */
const WebSkeleton = ({
  shape = 'default',
  block = false,
  width = '100%',
  height = '100%',
  borderRadius = 12,
  style = {},
  animated = true,
  showTitle = false,
  showParagraph = false,
}: {
  shape?: 'circle' | 'round' | 'square' | 'default'
  block?: boolean
  width?: number | string
  height?: number | string
  borderRadius?: number
  style?:
    | (React.CSSProperties & Partial<Record<'--width' | '--height' | '--border-radius', string>>)
    | undefined
  animated?: boolean
  showTitle?: boolean
  showParagraph?: boolean
}) => {
  const skeletonStyle = useMemo(
    () => ({
      backgroundColor: '#F8F8F9',
      ...(width && { width }),
      ...(height && { height }),
      ...(shape === 'circle' && { borderRadius: 9999 }), // 圆形
      ...(shape === 'round' && { borderRadius }), // 圆角矩形
      ...(shape === 'square' && { borderRadius: 0 }), // 正方形
      ...(shape === 'default' && { borderRadius }), // 默认矩形
      ...(block && { width: '100%' }), // 如果是块级元素，宽度填满父容器
      ...style, // 自定义样式
    }),
    [width, height, shape, block, style, borderRadius],
  )

  return (
    <Skeleton active={animated} style={skeletonStyle} title={showTitle} paragraph={showParagraph} />
  )
}

export default WebSkeleton
