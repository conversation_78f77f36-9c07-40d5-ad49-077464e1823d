/**
 * 测试规格选择弹窗的限流修复
 * 验证弹窗内的按钮是否正确响应限流状态
 */

import { render, screen } from '@testing-library/react'
import { ProductOptionsPopup } from '../index'

// Mock useProduct hook
const mockUseProduct = {
  productDetails: {
    name: 'Test Product',
    sku: 'TEST-SKU',
  },
  setVisibleAddCartPop: jest.fn(),
  safeguardItems: [],
  productConfigOptions: [],
  variants: [],
  productConfigurableOptions: [],
  handleUpdateService: jest.fn(),
  deliveryMethodPickup: false,
  selectStore: null,
  isBuy: false,
  handleSelectionChange: jest.fn(),
  handleQtyChange: jest.fn(),
  setDoorVisible: jest.fn(),
  buyNowLoading: false,
  onAddToCart: jest.fn(),
  addCartLoading: false,
  productStatus: {
    isEverythingOutOfStock: false,
    salable_qty: 10,
    price: {
      final_price: { value: 100 },
      regular_price: { value: 120 },
    },
  },
  isLoggedIn: true,
  addToCartRateLimit: {
    isDisabled: false,
    buttonText: '加入购物车',
  },
  buyNowRateLimit: {
    isDisabled: false,
    buttonText: '立即购买',
  },
}

jest.mock('../../context/ProductContext', () => ({
  useProduct: () => mockUseProduct,
}))

describe('ProductOptionsPopup Rate Limit Fix', () => {
  it('should disable add to cart button when rate limited', () => {
    // 模拟加入购物车被限流
    const rateLimitedMock = {
      ...mockUseProduct,
      addToCartRateLimit: {
        isDisabled: true,
        buttonText: '请稍后再试 (30s)',
      },
    }

    jest.doMock('../../context/ProductContext', () => ({
      useProduct: () => rateLimitedMock,
    }))

    render(<ProductOptionsPopup visible={true} onClose={jest.fn()} />)

    const button = screen.getByRole('button', { name: /请稍后再试/ })
    expect(button).toBeDisabled()
    expect(button).toHaveClass('nb-button-no-padding')
  })

  it('should disable buy now button when rate limited', () => {
    // 模拟立即购买被限流
    const rateLimitedMock = {
      ...mockUseProduct,
      isBuy: true,
      buyNowRateLimit: {
        isDisabled: true,
        buttonText: '请稍后再试 (15s)',
      },
    }

    jest.doMock('../../context/ProductContext', () => ({
      useProduct: () => rateLimitedMock,
    }))

    render(<ProductOptionsPopup visible={true} onClose={jest.fn()} />)

    const button = screen.getByRole('button', { name: /请稍后再试/ })
    expect(button).toBeDisabled()
    expect(button).toHaveClass('nb-button-no-padding')
  })

  it('should enable buttons when not rate limited', () => {
    render(<ProductOptionsPopup visible={true} onClose={jest.fn()} />)

    const button = screen.getByRole('button', { name: /加入购物车/ })
    expect(button).not.toBeDisabled()
    expect(button).not.toHaveClass('nb-button-no-padding')
  })
})
