import { NextRequest, NextResponse } from 'next/server'
import createMiddleware from 'next-intl/middleware'
import { createRouteMatcher } from '@clerk/nextjs/server'
import { generateEncryptedQUID, getAppConfig, KEY_TOKEN, QUID_KEY } from '@ninebot/core'

import { TLocales } from './i18n/type'
import { i18nRouting } from './config'

// i18n middleware
const handleI18nRouting = createMiddleware(i18nRouting)

// 需要保护的 Route
const isProtectedRoute = createRouteMatcher(getAppConfig().protectedRouteMatcher || [])

// 支持的 locale
const locales = i18nRouting.locales

// define middleware
export default function middleware(request: NextRequest) {
  const { cookies, headers } = request

  // 将当前请求的 url 存储在 header 中
  headers.set('x-url', request.nextUrl.pathname)

  // 拦截 .well-known 请求
  if (request.nextUrl.pathname.includes('.well-known')) {
    return new NextResponse('Not Found', { status: 404 })
  }

  // 从 cookie 获取 quid
  const quidFromCookie = cookies.get(QUID_KEY)?.value
  if (!quidFromCookie) {
    // 从 cookie 没有获取到 quid，生成一个 quid
    const quid = generateEncryptedQUID('h5')
    headers.set(QUID_KEY.toUpperCase(), quid ? quid : '')
  } else {
    // 从 cookie 获取到 quid，设置到 header 中
    headers.set(QUID_KEY.toUpperCase(), JSON.parse(quidFromCookie))
  }

  const nextUrl = request.nextUrl.clone()
  const [, maybeLocale] = nextUrl.pathname.split('/')

  // apply i18n routing
  const response = handleI18nRouting(request)

  // apply auth route
  if (isProtectedRoute(request)) {
    const tokenFormCookies = request.cookies.get(KEY_TOKEN)
    if (!tokenFormCookies?.value) {
      nextUrl.pathname = locales.includes(maybeLocale as TLocales) ? `/${maybeLocale}` : `/`
      return NextResponse.redirect(nextUrl)
    }
  }

  return response
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - api (API routes)
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     */
    '/((?!api|_next/static|_next/image|favicon.ico).*)',
  ],
  unstable_allowDynamic: [
    // 允许 lodash-es 模块使用动态代码评估
    '**/node_modules/lodash-es/**',
    '**/node_modules/vconsole/**',
  ],
}
