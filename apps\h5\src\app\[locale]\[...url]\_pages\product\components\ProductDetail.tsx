import { RenderHtml } from '@ninebot/core'

import { CustomEmpty } from '@/components'

/**
 * 商品详情
 */
const ProductDetail = ({ content }: { content: string }) => {
  if (!content) {
    return <CustomEmpty description={'暂无商品详情'} />
  }
  return (
    <div className="my-base flex flex-col gap-base-16 rounded-base-12 bg-white px-base-12 py-base-16 text-base">
      {/* <div className="font-miSansDemiBold450 text-lg">商品详情</div> */}
      <div className="rounded-lg">
        <RenderHtml content={content} />
      </div>
    </div>
  )
}

export default ProductDetail
