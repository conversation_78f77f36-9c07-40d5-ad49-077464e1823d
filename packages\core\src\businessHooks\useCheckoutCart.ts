'use client'

import { useCallback, useState } from 'react'
import { useDispatch } from 'react-redux'

import { useLazyGetCartQtyQuery } from '../services'
import { setAllCartProducts, TCheckoutCartProductItems } from '../store'
import { formatProducts, GqlError, PRECISE_RATE_LIMIT } from '../utils'

/**
 * 获取购物车信息 Hook
 */
const useCheckoutCart = () => {
  const dispatch = useDispatch()
  const [isRateLimited, setIsRateLimited] = useState(false)

  const [getCartQtyQuery, { isLoading }] = useLazyGetCartQtyQuery()

  /**
   * 获取购物车信息
   */
  const fetchCheckoutCart = useCallback(async () => {
    try {
      const response = await getCartQtyQuery({}).unwrap()
      if (response?.customer?.shipping_cart?.items) {
        dispatch(
          setAllCartProducts(
            formatProducts(response?.customer.shipping_cart.items as TCheckoutCartProductItems),
          ),
        )
      }
      // 成功获取数据，重置限流状态
      setIsRateLimited(false)
    } catch (error) {
      // 检查是否为限流错误
      const err = error as GqlError

      if (err?.type === PRECISE_RATE_LIMIT) {
        // 设置限流状态，不显示购物车数量
        setIsRateLimited(true)
        // 静默处理 427/428 限流错误，不显示 toast 提示
        console.log('getCartQty rate limited, retryMs:', err.retryMs)
      } else {
        // 如果不是限流错误，只记录到控制台，不显示给用户
        console.error('useCheckoutCart error:', error)
      }
    }
  }, [dispatch, getCartQtyQuery])

  return { fetchCheckoutCart, isLoading, isRateLimited }
}

export default useCheckoutCart
