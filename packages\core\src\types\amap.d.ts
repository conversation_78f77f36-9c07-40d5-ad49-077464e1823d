// 高德地图类型定义

export interface MassMarkerPoint {
  lnglat: [number, number]
  name: string
  style?: number
}

export interface MassMarkerStyle {
  url: string
  anchor: { x: number; y: number }
  size: { width: number; height: number }
  zIndex: number
}

export interface MassMarkerOptions {
  zIndex: number
  zooms: [number, number]
  style: MassMarkerStyle | MassMarkerStyle[]
}

export interface MassMarkerInstance {
  setMap: (map: AMapMap | null) => void
  setData: (data: MassMarkerPoint[]) => void
  on?: (event: string, callback: (event: MassMarkerClickEvent) => void) => void
}

export interface MassMarkerClickEvent {
  data: MassMarkerPoint
  lnglat: [number, number]
}

export interface AMap {
  Geolocation: new (options: AMapGeolocationOptions) => AMapGeolocation
  Geocoder: new (options?: { city?: string }) => AMapGeocoder
  Map: new (container: string, options: AMapMapOptions) => AMapMap
  Scale: new () => AMapScale
  MassMarks: new (data: MassMarkerPoint[], options: MassMarkerOptions) => MassMarkerInstance
  Pixel: new (x: number, y: number) => { x: number; y: number }
  Size: new (width: number, height: number) => { width: number; height: number }
  LngLat: new (lng: number, lat: number) => LngLat
}

export interface AMapMap {
  destroy: () => void
  addControl: (control: AMapScale) => void
  setCenter: (position: [number, number] | LngLat) => void
}

export interface AMapScale {
  show: () => void
  hide: () => void
}

export interface AMapMapOptions {
  viewMode: string
  zoom: number
  center?: [number, number]
}

export interface AMapGeolocationOptions {
  enableHighAccuracy: boolean
  timeout: number
  showButton: boolean
  showMarker: boolean
  showCircle: boolean
}

export interface AMapGeolocation {
  getCurrentPosition: (
    callback: (status: string, result: AMapGeolocationResult) => void,
    options?: { enableHighAccuracy: boolean; timeout: number; maximumAge: number },
  ) => void
}

export interface AMapGeolocationResult {
  position: {
    getLat: () => number
    getLng: () => number
  }
  formattedAddress?: string
  accuracy?: number
  message?: string
  info?: string
}

export interface AMapGeocoder {
  getAddress: (
    location: [number, number],
    callback: (status: string, result: RegeocodeResult) => void,
  ) => void
}

export interface RegeocodeResult {
  regeocode: {
    addressComponent: {
      province: string
      city: string
      district: string
    }
  }
  info?: string
  status?: string
}

export interface LocationInfo {
  latitude: number | null
  longitude: number | null
  address?: string
  accuracy?: number
  error?: string
}

export interface LngLat {
  getLng: () => number
  getLat: () => number
}

declare global {
  interface Window {
    AMap: AMap
    _AMapSecurityConfig: {
      securityJsCode: string
    }
    AMapLoader: {
      load: (config: { key: string; version: string; plugins: string[] }) => Promise<AMap>
    }
  }
}
